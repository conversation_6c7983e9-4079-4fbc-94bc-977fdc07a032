{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue?vue&type=style&index=0&id=134e98f7&scoped=true&lang=css", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue", "mtime": 1754373233745}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753353053523}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753353054636}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753353053916}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDoh6rlrprkuYnmraXpqqTmnaHmoLflvI8gLSDmv4DmtLvoioLngrnkuLrok53oibIgKi8NCjo6di1kZWVwIC5jdXN0b20tc3RlcHMgLmVsLXN0ZXBfX2hlYWQuaXMtcHJvY2VzcyB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICBib3JkZXItY29sb3I6ICM0MDllZmY7DQp9DQoNCjo6di1kZWVwIC5jdXN0b20tc3RlcHMgLmVsLXN0ZXBfX2hlYWQuaXMtcHJvY2VzcyAuZWwtc3RlcF9faWNvbiB7DQogIGJhY2tncm91bmQtY29sb3I6ICM0MDllZmY7DQogIGJvcmRlci1jb2xvcjogIzQwOWVmZjsNCiAgY29sb3I6ICNmZmY7DQp9DQoNCjo6di1kZWVwIC5jdXN0b20tc3RlcHMgLmVsLXN0ZXBfX3RpdGxlLmlzLXByb2Nlc3Mgew0KICBjb2xvcjogIzQwOWVmZjsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQo="}, {"version": 3, "sources": ["litigationLogView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoMA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "litigationLogView.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"日志查看\" width=\"800px\" @close=\"handleCancel\">\r\n      <!-- 进度条或撤诉状态 -->\r\n      <div v-if=\"isWithdrawn\" style=\"text-align: center; margin-bottom: 24px; padding: 20px; background-color: #f5f5f5; border-radius: 4px;\">\r\n        <span style=\"font-size: 16px; color: #909399; font-weight: bold;\">已撤案</span>\r\n      </div>\r\n      <el-steps v-else :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 日志表格 -->\r\n      <el-table :data=\"logList\" border style=\"width: 100%; margin-bottom: 24px\">\r\n        <el-table-column prop=\"createTime\" label=\"时间\" width=\"160\" />\r\n        <el-table-column prop=\"createBy\" label=\"跟踪人\" width=\"120\" />\r\n        <el-table-column prop=\"status\" label=\"跟踪动作\" width=\"120\" />\r\n        <el-table-column prop=\"remark\" label=\"描述\" />\r\n      </el-table>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\">催记日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        listLitigation_log({ litigationCaseId: newVal.id }).then(res => {\r\n          this.logList = res.rows\r\n          // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n          if (res.rows && res.rows.length > 0) {\r\n            const lastLogStatus = res.rows[res.rows.length - 1].status\r\n            this.currentStatus = lastLogStatus\r\n            this.setActiveStepByStatus(lastLogStatus)\r\n          }\r\n        })\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      litigationStatusTree: [\r\n        // 独立状态（不需要分类）\r\n        { label: '暂不起诉', value: '暂不起诉' },\r\n        { label: '撤案', value: '撤案' },\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '获取案件号', value: '获取案件号' },\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待执行', value: '待执行' },\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '执行终本', value: '执行终本' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n      // 当前状态\r\n      currentStatus: '',\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤（排除独立状态）\r\n    statusSteps() {\r\n      return this.litigationStatusTree\r\n        .filter(item => item.children && item.children.length > 0)\r\n        .map(item => item.label)\r\n    },\r\n    // 判断是否为撤案状态\r\n    isWithdrawn() {\r\n      return this.currentStatus === '撤案'\r\n    },\r\n  },\r\n  methods: {\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 如果是撤案状态，不设置进度条\r\n      if (status === '撤案') {\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n    },\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    handleConfirm() {\r\n      return\r\n    },\r\n    handleCancel() {\r\n      this.visible = false\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"]}]}