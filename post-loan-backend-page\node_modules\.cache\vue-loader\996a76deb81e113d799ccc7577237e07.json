{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue", "mtime": 1754373233745}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["litigationLogView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigationLogView.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"日志查看\" width=\"800px\" @close=\"handleCancel\">\r\n      <!-- 进度条或撤诉状态 -->\r\n      <div v-if=\"isWithdrawn\" style=\"text-align: center; margin-bottom: 24px; padding: 20px; background-color: #f5f5f5; border-radius: 4px;\">\r\n        <span style=\"font-size: 16px; color: #909399; font-weight: bold;\">已撤案</span>\r\n      </div>\r\n      <el-steps v-else :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 日志表格 -->\r\n      <el-table :data=\"logList\" border style=\"width: 100%; margin-bottom: 24px\">\r\n        <el-table-column prop=\"createTime\" label=\"时间\" width=\"160\" />\r\n        <el-table-column prop=\"createBy\" label=\"跟踪人\" width=\"120\" />\r\n        <el-table-column prop=\"status\" label=\"跟踪动作\" width=\"120\" />\r\n        <el-table-column prop=\"remark\" label=\"描述\" />\r\n      </el-table>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\">催记日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        listLitigation_log({ litigationCaseId: newVal.id }).then(res => {\r\n          this.logList = res.rows\r\n          // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n          if (res.rows && res.rows.length > 0) {\r\n            const lastLogStatus = res.rows[res.rows.length - 1].status\r\n            this.currentStatus = lastLogStatus\r\n            this.setActiveStepByStatus(lastLogStatus)\r\n          }\r\n        })\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      litigationStatusTree: [\r\n        // 独立状态（不需要分类）\r\n        { label: '暂不起诉', value: '暂不起诉' },\r\n        { label: '撤案', value: '撤案' },\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '获取案件号', value: '获取案件号' },\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待执行', value: '待执行' },\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '执行终本', value: '执行终本' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n      // 当前状态\r\n      currentStatus: '',\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤（排除独立状态）\r\n    statusSteps() {\r\n      return this.litigationStatusTree\r\n        .filter(item => item.children && item.children.length > 0)\r\n        .map(item => item.label)\r\n    },\r\n    // 判断是否为撤案状态\r\n    isWithdrawn() {\r\n      return this.currentStatus === '撤案'\r\n    },\r\n  },\r\n  methods: {\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 如果是撤案状态，不设置进度条\r\n      if (status === '撤案') {\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n    },\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    handleConfirm() {\r\n      return\r\n    },\r\n    handleCancel() {\r\n      this.visible = false\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"]}]}