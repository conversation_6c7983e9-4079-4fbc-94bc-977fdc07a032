{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue", "mtime": 1754371540876}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["litigationLogView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2FA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigationLogView.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"日志查看\" width=\"1200px\" @close=\"handleCancel\">\r\n      <!-- 进度条 -->\r\n      <el-steps :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 日志表格 -->\r\n      <el-table v-loading=\"loading\" :data=\"logList\" border style=\"width: 100%; margin-bottom: 24px\" element-loading-text=\"加载日志数据中...\">\r\n        <el-table-column prop=\"createTime\" label=\"时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDateTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"createBy\" label=\"跟踪人\" width=\"120\" />\r\n        <el-table-column prop=\"status\" label=\"跟踪动作\" width=\"120\" />\r\n        <el-table-column label=\"文书信息\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.docName || scope.row.docNumber\">\r\n              <div v-if=\"scope.row.docName\" style=\"margin-bottom: 4px;\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.docName }}</el-tag>\r\n              </div>\r\n              <div v-if=\"scope.row.docNumber\" style=\"font-size: 12px; color: #666;\">\r\n                文书号：{{ scope.row.docNumber }}\r\n              </div>\r\n            </div>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"开庭时间\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.openDate\">{{ formatDate(scope.row.openDate) }}</span>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"文书生效时间\" width=\"140\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.docEffectiveDate\">{{ formatDateTime(scope.row.docEffectiveDate) }}</span>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"文书附件\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"scope.row.docUploadUrl\"\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewDocuments(scope.row.docUploadUrl)\"\r\n            >\r\n              查看附件\r\n            </el-button>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"urgeDescribe\" label=\"描述\" min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.urgeDescribe\">{{ scope.row.urgeDescribe }}</span>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\">催记日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n\r\n    <!-- 文档预览对话框 -->\r\n    <el-dialog :visible.sync=\"docPreviewVisible\" title=\"文档附件\" width=\"400px\">\r\n      <div style=\"max-height: 300px; overflow-y: auto;\">\r\n        <div v-for=\"(doc, index) in docPreviewList\" :key=\"index\" style=\"margin-bottom: 10px;\">\r\n          <div class=\"doc-preview-btn\" @click=\"window.open(doc.url, '_blank')\" style=\"cursor: pointer;\">\r\n            <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409eff;\"></i>\r\n            {{ doc.name }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"docPreviewVisible = false\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport { loan_reminder_order } from '@/api/reminde_view/reminde_view'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        if (newVal && newVal.id) {\r\n          this.loadLitigationLogs(newVal.id, newVal.流程序号)\r\n        }\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      loading: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      // 文档预览相关\r\n      docPreviewVisible: false,\r\n      docPreviewList: [],\r\n      litigationStatusTree: [\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '撤案已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '执行终本', value: '执行终本' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n        { label: '撤案', value: '撤案' },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤\r\n    statusSteps() {\r\n      return this.litigationStatusTree.map(item => item.label)\r\n    },\r\n  },\r\n  methods: {\r\n    // 加载法诉日志数据\r\n    async loadLitigationLogs(litigationCaseId, loanId) {\r\n      this.loading = true\r\n      try {\r\n        // 获取法诉日志\r\n        const litigationLogRes = await listLitigation_log({ litigationCaseId })\r\n\r\n        // 获取法诉相关的催记日志（status=2表示法诉日志）\r\n        const reminderLogRes = await loan_reminder_order({\r\n          loanId: loanId,\r\n          status: 2,\r\n          litigationId: litigationCaseId\r\n        })\r\n\r\n        // 合并数据\r\n        const combinedLogs = []\r\n\r\n        // 添加法诉日志\r\n        if (litigationLogRes.rows) {\r\n          litigationLogRes.rows.forEach(log => {\r\n            combinedLogs.push({\r\n              ...log,\r\n              type: 'litigation',\r\n              urgeDescribe: log.remark || '-'\r\n            })\r\n          })\r\n        }\r\n\r\n        // 添加催记日志\r\n        if (reminderLogRes.rows) {\r\n          reminderLogRes.rows.forEach(reminder => {\r\n            combinedLogs.push({\r\n              ...reminder,\r\n              type: 'reminder',\r\n              status: this.getUrgeStatusText(reminder.urgeStatus),\r\n              docName: '',\r\n              docNumber: '',\r\n              docUploadUrl: '',\r\n              openDate: '',\r\n              docEffectiveDate: ''\r\n            })\r\n          })\r\n        }\r\n\r\n        // 按创建时间排序\r\n        combinedLogs.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))\r\n\r\n        this.logList = combinedLogs\r\n\r\n        // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n        if (combinedLogs.length > 0) {\r\n          const lastLogStatus = combinedLogs[0].status\r\n          this.setActiveStepByStatus(lastLogStatus)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载法诉日志失败:', error)\r\n        this.$message.error('加载日志数据失败')\r\n      }\r\n    },\r\n\r\n    // 获取催记状态文本\r\n    getUrgeStatusText(status) {\r\n      const statusMap = {\r\n        1: '继续跟踪',\r\n        2: '约定还款',\r\n        3: '无法跟进',\r\n        4: '暂时无需跟进'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return '-'\r\n      const date = new Date(dateTime)\r\n      return date.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit'\r\n      })\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      if (!date) return '-'\r\n      const d = new Date(date)\r\n      return d.toLocaleDateString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit'\r\n      })\r\n    },\r\n\r\n    // 查看文档附件\r\n    viewDocuments(docUrls) {\r\n      if (!docUrls) return\r\n\r\n      let urls = []\r\n      if (typeof docUrls === 'string') {\r\n        try {\r\n          urls = JSON.parse(docUrls)\r\n        } catch (e) {\r\n          urls = [docUrls]\r\n        }\r\n      } else if (Array.isArray(docUrls)) {\r\n        urls = docUrls\r\n      }\r\n\r\n      if (urls.length > 0) {\r\n        // 如果只有一个文件，直接打开\r\n        if (urls.length === 1) {\r\n          window.open(urls[0], '_blank')\r\n        } else {\r\n          // 多个文件，显示列表让用户选择\r\n          this.docPreviewList = urls.map((url, index) => ({\r\n            name: `附件${index + 1}`,\r\n            url: url\r\n          }))\r\n          this.docPreviewVisible = true\r\n        }\r\n      }\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n    },\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    handleConfirm() {\r\n      return\r\n    },\r\n    handleCancel() {\r\n      this.visible = false\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 表格样式优化 */\r\n::v-deep .el-table .el-table__cell {\r\n  padding: 8px 0;\r\n}\r\n\r\n::v-deep .el-table .el-table__header th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 文档预览按钮样式 */\r\n.doc-preview-btn {\r\n  width: 100%;\r\n  text-align: left;\r\n  padding: 8px 12px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.doc-preview-btn:hover {\r\n  background-color: #f5f7fa;\r\n  border-color: #409eff;\r\n  color: #409eff;\r\n}\r\n</style>\r\n"]}]}